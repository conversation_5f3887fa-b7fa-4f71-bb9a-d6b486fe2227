<template>
  <div class="user-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="breadcrumb">
        <span class="breadcrumb-item">用户管理</span>
      </div>
    </div>

    <!-- 操作栏 -->
    <div class="operation-bar">
      <div class="left-actions">
        <el-button type="primary" size="small">
          <el-icon>
            <Plus />
          </el-icon>
          新增用户
        </el-button>
      </div>
      <div class="right-actions">
        <el-input v-model="searchKeyword" placeholder="请输入昵称搜索" class="search-input" size="small" clearable @keyup.enter="handleSearch" @clear="handleClear">
          <template #append>
            <el-button @click="handleSearch">
              <el-icon>
                <Search />
              </el-icon>
              搜索
            </el-button>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table :data="userList" v-loading="loading" class="data-table" stripe border :header-cell-style="{ background: '#fafafa', color: '#333' }">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="id" label="用户ID" min-width="80" align="center" />
        <el-table-column prop="phone" label="手机号" min-width="130" align="center" />
        <el-table-column prop="nickname" label="昵称" min-width="120">
          <template #default="scope">
            <span>{{ scope.row.nickname || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="头像" min-width="80" align="center">
          <template #default="scope">
            <el-avatar :size="32" :src="scope.row.avatar" class="user-avatar">
              <el-icon>
                <User />
              </el-icon>
            </el-avatar>
          </template>
        </el-table-column>
        <el-table-column label="会员状态" min-width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.is_member ? 'success' : 'info'" size="small">
              {{ scope.row.is_member ? '会员' : '普通用户' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="剩余次数" min-width="100" align="center">
          <template #default="scope">
            <span :class="{ 'low-usage': scope.row.remaining_usage <= 5 && scope.row.is_member }">
              {{ scope.row.is_member ? scope.row.remaining_usage : '-' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="注册时间" min-width="160" align="center">
          <template #default="scope">
            <span>{{ formatDate(scope.row.created_at) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="updated_at" label="最后更新" min-width="160" align="center">
          <template #default="scope">
            <span>{{ formatDate(scope.row.updated_at) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center" fixed="right">
          <template #default="scope">
            <el-button type="text" size="small" @click="viewUser(scope.row)">
              开通会员
            </el-button>
            <el-button type="text" size="small" @click="editUser(scope.row)">
              编辑会员
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <el-pagination class="pagination" :current-page="currentPage" :page-size="pageSize" :page-sizes="[10, 20, 50, 100]" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, User, Plus } from '@element-plus/icons-vue'
import { getUserList } from '@/api/user'

// 响应式数据
const loading = ref(false)
const userList = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(50)  // 修改默认分页大小为50
const searchKeyword = ref('')

// 获取用户列表
const fetchUserList = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value
    }

    // 如果有搜索关键词，添加昵称搜索参数
    if (searchKeyword.value && searchKeyword.value.trim()) {
      params.nickname = searchKeyword.value.trim()
    }

    const response = await getUserList(params)

    if (response.code === 0) {
      userList.value = response.data.users
      total.value = response.data.total
    } else {
      ElMessage.error(response.message || '获取用户列表失败')
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  fetchUserList()
}

// 清空搜索
const handleClear = () => {
  searchKeyword.value = ''
  currentPage.value = 1
  fetchUserList()
}

// 分页大小改变
const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
  fetchUserList()
}

// 当前页改变
const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
  fetchUserList()
}

// 查看用户
const viewUser = (user) => {
  ElMessage.info(`查看用户: ${user.phone}`)
}

// 编辑用户
const editUser = (user) => {
  ElMessage.info(`编辑用户: ${user.phone}`)
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 组件挂载时获取数据
onMounted(() => {
  fetchUserList()
})
</script>

<style scoped>
.pagination {
  position: fixed;
  bottom: 0;
  left: 10px; /* 侧边栏宽度 */
  right: 0;
  display: flex;
  justify-content: flex-start;
  padding: 16px;
}
.user-management {
  padding: 20px;
  padding-bottom: 80px; /* 为底部分页留出空间 */
  background: #f5f5f5;
  min-height: 100vh;
  position: relative;
}

/* 页面头部 */
.page-header {
  margin-bottom: 16px;
}

.breadcrumb {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.breadcrumb-item {
  color: #333;
}

/* 操作栏 */
.operation-bar {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.left-actions {
  display: flex;
  gap: 8px;
}

.right-actions {
  display: flex;
  align-items: center;
  margin-left: 18px;
}

.search-input {
  width: 250px;
}

.search-input :deep(.el-input-group__append) {
  background: #409eff;
  border-color: #409eff;
  color: white;
}

.search-input :deep(.el-input-group__append .el-button) {
  background: transparent;
  border: none;
  color: white;
  margin: 0;
  padding: 0 15px;
}

.search-input :deep(.el-input-group__append .el-button:hover) {
  background: rgba(255, 255, 255, 0.1);
}

/* 表格容器 */
.table-container {
  background: white;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 16px;
  max-height: calc(100vh - 200px); /* 减去头部、操作栏和分页的高度 */
  overflow-y: auto;
}

/* 数据表格 */
.data-table {
  width: 100%;
}

.data-table :deep(.el-table__header-wrapper) {
  background: #fafafa;
}

.data-table :deep(.el-table__header th) {
  background: #fafafa;
  color: #333;
  font-weight: 500;
  border-bottom: 1px solid #ebeef5;
  padding: 12px 0;
}

.data-table :deep(.el-table__body tr) {
  transition: background-color 0.3s;
}

.data-table :deep(.el-table__body tr:hover) {
  background-color: #f5f7fa;
}

.data-table :deep(.el-table__body td) {
  padding: 12px 0;
  border-bottom: 1px solid #ebeef5;
}

.data-table :deep(.el-table__body .el-button--text) {
  color: #409eff;
  padding: 0;
  margin-right: 8px;
}

.data-table :deep(.el-table__body .el-button--text:hover) {
  color: #66b1ff;
}

/* 用户头像 */
.user-avatar {
  background: #409eff;
}

/* 剩余次数低时的警告样式 */
.low-usage {
  color: #f56c6c;
  font-weight: bold;
}

/* 分页容器 */
.pagination-container {
  position: fixed;
  bottom: 0;
  left: 200px; /* 侧边栏宽度 */
  right: 0;
  display: flex;
  justify-content: center;
  padding: 16px;
  background: white;
  border-top: 1px solid #e4e7ed;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.pagination-container :deep(.el-pagination) {
  display: flex;
  align-items: center;
}

.pagination-container :deep(.el-pagination__total) {
  color: #606266;
  font-size: 13px;
}

.pagination-container :deep(.el-pagination__sizes) {
  margin-left: 10px;
}

.pagination-container :deep(.el-pagination__jump) {
  margin-left: 24px;
  color: #606266;
  font-size: 13px;
}

.pagination-container :deep(.el-pager li) {
  background: white;
  color: #606266;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  margin: 0 2px;
}

.pagination-container :deep(.el-pager li:hover) {
  color: #409eff;
}

.pagination-container :deep(.el-pager li.is-active) {
  background: #409eff;
  color: white;
  border-color: #409eff;
}

.pagination-container :deep(.btn-prev),
.pagination-container :deep(.btn-next) {
  background: white;
  color: #606266;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.pagination-container :deep(.btn-prev:hover),
.pagination-container :deep(.btn-next:hover) {
  color: #409eff;
}

.pagination-container :deep(.btn-prev:disabled),
.pagination-container :deep(.btn-next:disabled) {
  color: #c0c4cc;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-management {
    padding: 10px;
    padding-bottom: 80px;
  }

  .operation-bar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .left-actions,
  .right-actions {
    justify-content: center;
  }

  .search-input {
    width: 100%;
  }

  .data-table :deep(.el-table__body .el-button--text) {
    display: block;
    margin: 2px 0;
  }

  .pagination-container {
    left: 0; /* 移动端时从屏幕左边开始 */
  }

  .table-container {
    max-height: calc(100vh - 250px); /* 移动端调整高度 */
  }
}

/* 表格卡片样式 */
.table-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin-bottom: 24px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e2e8f0;
}

.table-title {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.table-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.table-content {
  padding: 0;
}

/* 现代化表格样式 */
.modern-table {
  width: 100%;
}

.modern-table :deep(.el-table__header-wrapper) {
  border-radius: 0;
}

.modern-table :deep(.el-table__body-wrapper) {
  border-radius: 0;
}

.modern-table :deep(.el-table__row) {
  transition: all 0.3s ease;
}

.modern-table :deep(.el-table__row:hover) {
  background-color: #f8fafc !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 表格单元格样式 */
.phone-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.phone-icon {
  color: #667eea;
  font-size: 16px;
}

.phone-text {
  font-weight: 500;
  color: #374151;
}

.nickname-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.nickname-text {
  font-weight: 500;
  color: #374151;
}

.avatar-cell {
  display: flex;
  justify-content: center;
  align-items: center;
}

.user-avatar {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: 3px solid #f3f4f6;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
  transition: all 0.3s ease;
}

.user-avatar:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.3);
}

.avatar-icon {
  font-size: 24px;
  color: white;
}

.time-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.time-icon {
  color: #6b7280;
  font-size: 16px;
  flex-shrink: 0;
}

.time-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.time-main {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.time-sub {
  font-size: 12px;
  color: #9ca3af;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.action-btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  padding: 8px 16px;
}

.action-btn:hover {
  transform: translateY(-1px);
}

.action-btn.el-button--primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border: none;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.action-btn.el-button--primary:hover {
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.action-btn.el-button--warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  border: none;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

.action-btn.el-button--warning:hover {
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
}

/* 分页卡片样式 */
.pagination-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  padding: 24px 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-info {
  flex: 1;
}

.pagination-text {
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
}

.modern-pagination {
  flex-shrink: 0;
}

.modern-pagination :deep(.el-pagination__sizes) {
  margin-right: 16px;
}

.modern-pagination :deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.modern-pagination :deep(.el-select .el-input__wrapper:hover) {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
}

.modern-pagination :deep(.btn-prev),
.modern-pagination :deep(.btn-next) {
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  background: white;
  transition: all 0.3s ease;
}

.modern-pagination :deep(.btn-prev:hover),
.modern-pagination :deep(.btn-next:hover) {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-color: transparent;
  transform: translateY(-1px);
}

.modern-pagination :deep(.el-pager li) {
  border-radius: 8px;
  margin: 0 4px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.modern-pagination :deep(.el-pager li:hover) {
  background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
  transform: translateY(-1px);
}

.modern-pagination :deep(.el-pager li.is-active) {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-color: transparent;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.modern-pagination :deep(.el-pagination__jump) {
  margin-left: 16px;
}

.modern-pagination :deep(.el-pagination__jump .el-input__wrapper) {
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.modern-pagination :deep(.el-pagination__jump .el-input__wrapper:hover) {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    gap: 24px;
    text-align: center;
  }

  .header-stats {
    justify-content: center;
  }

  .toolbar-content {
    flex-direction: column;
    gap: 20px;
  }

  .search-wrapper {
    width: 100%;
    justify-content: center;
  }

  .pagination-card {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
}

@media (max-width: 768px) {
  .user-list-container {
    padding: 16px;
  }

  .page-title {
    font-size: 24px;
  }

  .header-content {
    padding: 24px;
  }

  .toolbar-content {
    padding: 16px;
  }

  .table-header {
    padding: 16px 20px;
  }

  .pagination-card {
    padding: 16px 20px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .action-btn {
    width: 100%;
    justify-content: center;
  }
}
</style>
