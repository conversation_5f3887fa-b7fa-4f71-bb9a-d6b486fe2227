import { get, post } from './request'

/**
 * 获取用户列表
 * @param {Object} params 查询参数
 * @param {number} params.page 页码，从1开始
 * @param {number} params.page_size 每页数量
 * @returns {Promise} 返回用户列表数据
 */
export const getUserList = (params) => {
  return get('/users/list', params)
}

/**
 * 获取用户详情
 * @param {number} userId 用户ID
 * @returns {Promise} 返回用户详情数据
 */
export const getUserDetail = (userId) => {
  return get(`/users/${userId}`)
}

/**
 * 更新用户信息
 * @param {number} userId 用户ID
 * @param {Object} userData 用户数据
 * @returns {Promise} 返回更新结果
 */
export const updateUser = (userId, userData) => {
  return post(`/users/${userId}/update`, userData)
}

/**
 * 删除用户
 * @param {number} userId 用户ID
 * @returns {Promise} 返回删除结果
 */
export const deleteUser = (userId) => {
  return post(`/users/${userId}/delete`)
}

/**
 * 搜索用户
 * @param {Object} params 搜索参数
 * @param {string} params.keyword 搜索关键词
 * @param {number} params.page 页码
 * @param {number} params.page_size 每页数量
 * @returns {Promise} 返回搜索结果
 */
export const searchUsers = (params) => {
  return get('/users/search', params)
}

/**
 * 开通会员
 * @param {string} userId 用户ID
 * @returns {Promise} 返回开通结果
 */
export const createMembership = (userId) => {
  return post('/ai-usage/create', { user_id: userId })
}
