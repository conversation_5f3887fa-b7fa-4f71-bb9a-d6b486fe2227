import{_ as F,u as G,n as H,r as J,p as K,q as O,b as l,o as Q,c as W,f as t,w as e,t as X,e as o,v as m,j as a,h as i,x as Y,y as Z,E as v,l as w,z as $,A as ss,B as b,C as x,D as y}from"./index-46ddd662.js";const ts={class:"dashboard-container"},os={class:"header-right"},es={class:"header-actions"},ns={class:"welcome-text"},ls={class:"stats-grid"},as={class:"stats-content"},is={class:"stats-icon users-icon"},ds={class:"stats-info"},us={class:"stats-trend positive"},cs={class:"stats-content"},_s={class:"stats-icon content-icon"},rs={class:"stats-info"},fs={class:"stats-trend positive"},ms={class:"stats-content"},ps={class:"stats-icon revenue-icon"},vs={class:"stats-info"},ws={class:"stats-trend positive"},gs={class:"stats-content"},hs={class:"stats-icon activity-icon"},bs={class:"stats-info"},xs={class:"stats-trend positive"},ys={class:"welcome-section"},zs={class:"welcome-content"},Cs={class:"welcome-text"},Ds={class:"welcome-title"},Vs={class:"welcome-info"},Bs={class:"info-item"},Ms={class:"info-value"},Es={class:"info-item"},Ts={class:"info-value"},Ls={class:"welcome-illustration"},Ns={class:"quick-actions"},Ss={class:"actions-grid"},ks={class:"action-content"},qs={class:"action-content"},As={class:"action-content"},Is={class:"action-content"},Ps={__name:"DashboardView",setup(Rs){const z=G(),C=H(),r=J(null),D=K(()=>C.path),V=()=>{r.value=X()},B=u=>u.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}),M=u=>{console.log("选择菜单:",u)},E=async u=>{if(u==="logout")try{await Y.confirm("确定要退出登录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await Z(),v.success("已退出登录"),z.push("/login")}catch(s){s!=="cancel"&&console.error("退出登录失败:",s)}else u==="profile"?v.info("个人资料功能开发中..."):u==="settings"&&v.info("账户设置功能开发中...")};return O(()=>{V()}),(u,s)=>{const n=l("el-icon"),T=l("el-button"),p=l("el-dropdown-item"),L=l("el-dropdown-menu"),N=l("el-dropdown"),S=l("el-header"),c=l("el-menu-item"),k=l("el-menu"),q=l("el-aside"),f=l("TrendCharts"),d=l("el-card"),A=l("Money"),I=l("DataLine"),P=l("Plus"),R=l("Edit"),U=l("View"),j=l("el-main"),g=l("el-container");return Q(),W("div",ts,[t(g,null,{default:e(()=>[t(S,{class:"header"},{default:e(()=>{var _;return[s[3]||(s[3]=o("div",{class:"header-left"},[o("div",{class:"logo-section"},[o("span",{class:"logo-text"},"爱收藏管理后台")])],-1)),o("div",os,[o("div",es,[o("span",ns,"欢迎，"+m(((_=r.value)==null?void 0:_.account)||"管理员"),1),t(N,{onCommand:E,class:"user-dropdown"},{dropdown:e(()=>[t(L,null,{default:e(()=>[t(p,{command:"profile"},{default:e(()=>s[0]||(s[0]=[a("个人资料")])),_:1,__:[0]}),t(p,{command:"settings"},{default:e(()=>s[1]||(s[1]=[a("账户设置")])),_:1,__:[1]}),t(p,{divided:"",command:"logout"},{default:e(()=>s[2]||(s[2]=[a("退出登录")])),_:1,__:[2]})]),_:1})]),default:e(()=>[t(T,{type:"text",class:"user-btn"},{default:e(()=>[t(n,null,{default:e(()=>[t(i(w))]),_:1}),t(n,{class:"dropdown-icon"},{default:e(()=>[t(i($))]),_:1})]),_:1})]),_:1})])])]}),_:1,__:[3]}),t(g,null,{default:e(()=>[t(q,{width:"200px",class:"sidebar"},{default:e(()=>[t(k,{"default-active":D.value,class:"sidebar-menu",onSelect:M,router:""},{default:e(()=>[t(c,{index:"/",class:"menu-item"},{title:e(()=>s[4]||(s[4]=[a("首页")])),default:e(()=>[t(n,null,{default:e(()=>[t(i(ss))]),_:1})]),_:1}),t(c,{index:"/users/list",class:"menu-item"},{title:e(()=>s[5]||(s[5]=[a("用户管理")])),default:e(()=>[t(n,null,{default:e(()=>[t(i(w))]),_:1})]),_:1}),t(c,{index:"/content/list",class:"menu-item"},{title:e(()=>s[6]||(s[6]=[a("内容管理")])),default:e(()=>[t(n,null,{default:e(()=>[t(i(b))]),_:1})]),_:1}),t(c,{index:"/analytics",class:"menu-item"},{title:e(()=>s[7]||(s[7]=[a("数据分析")])),default:e(()=>[t(n,null,{default:e(()=>[t(i(x))]),_:1})]),_:1}),t(c,{index:"/settings",class:"menu-item"},{title:e(()=>s[8]||(s[8]=[a("系统设置")])),default:e(()=>[t(n,null,{default:e(()=>[t(i(y))]),_:1})]),_:1})]),_:1},8,["default-active"])]),_:1}),t(j,{class:"main-content"},{default:e(()=>[o("div",ls,[t(d,{class:"stats-card",shadow:"hover"},{default:e(()=>[o("div",as,[o("div",is,[t(n,{size:"24"},{default:e(()=>[t(i(w))]),_:1})]),o("div",ds,[s[10]||(s[10]=o("h3",{class:"stats-number"},"1,234",-1)),s[11]||(s[11]=o("p",{class:"stats-label"},"总用户数",-1)),o("span",us,[t(n,null,{default:e(()=>[t(f)]),_:1}),s[9]||(s[9]=a(" +12.5% "))])])])]),_:1}),t(d,{class:"stats-card",shadow:"hover"},{default:e(()=>[o("div",cs,[o("div",_s,[t(n,{size:"24"},{default:e(()=>[t(i(b))]),_:1})]),o("div",rs,[s[13]||(s[13]=o("h3",{class:"stats-number"},"5,678",-1)),s[14]||(s[14]=o("p",{class:"stats-label"},"内容总数",-1)),o("span",fs,[t(n,null,{default:e(()=>[t(f)]),_:1}),s[12]||(s[12]=a(" +8.3% "))])])])]),_:1}),t(d,{class:"stats-card",shadow:"hover"},{default:e(()=>[o("div",ms,[o("div",ps,[t(n,{size:"24"},{default:e(()=>[t(A)]),_:1})]),o("div",vs,[s[16]||(s[16]=o("h3",{class:"stats-number"},"¥89,012",-1)),s[17]||(s[17]=o("p",{class:"stats-label"},"本月收入",-1)),o("span",ws,[t(n,null,{default:e(()=>[t(f)]),_:1}),s[15]||(s[15]=a(" +15.2% "))])])])]),_:1}),t(d,{class:"stats-card",shadow:"hover"},{default:e(()=>[o("div",gs,[o("div",hs,[t(n,{size:"24"},{default:e(()=>[t(I)]),_:1})]),o("div",bs,[s[19]||(s[19]=o("h3",{class:"stats-number"},"98.5%",-1)),s[20]||(s[20]=o("p",{class:"stats-label"},"系统活跃度",-1)),o("span",xs,[t(n,null,{default:e(()=>[t(f)]),_:1}),s[18]||(s[18]=a(" +2.1% "))])])])]),_:1})]),o("div",ys,[t(d,{class:"welcome-card",shadow:"never"},{default:e(()=>{var _,h;return[o("div",zs,[o("div",Cs,[o("h2",Ds,"欢迎回来，"+m(((_=r.value)==null?void 0:_.account)||"管理员")+"！",1),s[23]||(s[23]=o("p",{class:"welcome-subtitle"},"今天是个美好的一天，让我们开始工作吧",-1)),o("div",Vs,[o("div",Bs,[s[21]||(s[21]=o("span",{class:"info-label"},"用户ID:",-1)),o("span",Ms,m((h=r.value)==null?void 0:h.user_id),1)]),o("div",Es,[s[22]||(s[22]=o("span",{class:"info-label"},"最后登录:",-1)),o("span",Ts,m(B(new Date)),1)])])]),o("div",Ls,[t(n,{size:"120",class:"welcome-icon"},{default:e(()=>[t(i(x))]),_:1})])])]}),_:1})]),o("div",Ns,[s[32]||(s[32]=o("h3",{class:"section-title"},"快速操作",-1)),o("div",Ss,[t(d,{class:"action-card",shadow:"hover"},{default:e(()=>[o("div",ks,[t(n,{class:"action-icon",size:"32"},{default:e(()=>[t(P)]),_:1}),s[24]||(s[24]=o("h4",null,"添加用户",-1)),s[25]||(s[25]=o("p",null,"快速添加新用户",-1))])]),_:1}),t(d,{class:"action-card",shadow:"hover"},{default:e(()=>[o("div",qs,[t(n,{class:"action-icon",size:"32"},{default:e(()=>[t(R)]),_:1}),s[26]||(s[26]=o("h4",null,"内容管理",-1)),s[27]||(s[27]=o("p",null,"管理系统内容",-1))])]),_:1}),t(d,{class:"action-card",shadow:"hover"},{default:e(()=>[o("div",As,[t(n,{class:"action-icon",size:"32"},{default:e(()=>[t(U)]),_:1}),s[28]||(s[28]=o("h4",null,"数据报表",-1)),s[29]||(s[29]=o("p",null,"查看详细报表",-1))])]),_:1}),t(d,{class:"action-card",shadow:"hover"},{default:e(()=>[o("div",Is,[t(n,{class:"action-icon",size:"32"},{default:e(()=>[t(i(y))]),_:1}),s[30]||(s[30]=o("h4",null,"系统设置",-1)),s[31]||(s[31]=o("p",null,"配置系统参数",-1))])]),_:1})])])]),_:1})]),_:1})]),_:1})])}}},js=F(Ps,[["__scopeId","data-v-57a99516"]]);export{js as default};
