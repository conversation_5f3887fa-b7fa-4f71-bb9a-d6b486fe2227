import{F as T,G as j,_ as q,r as d,q as F,b as o,H as G,o as k,c as H,e as l,f as e,w as n,i as J,I as P,J as A,E as _,h as b,j as m,v as p,K as O,x as Q,L as R,M as W,l as X}from"./index-46ddd662.js";const Y=g=>T("/users/list",g),Z=g=>j("/ai-usage/create",{user_id:g});const ee={class:"user-management"},ae={class:"operation-bar"},te={class:"left-actions"},ne={class:"right-actions"},se={class:"table-container"},le={__name:"UserListView",setup(g){const v=d(!1),y=d([]),C=d(0),i=d(1),w=d(50),c=d(""),u=async()=>{v.value=!0;try{const s={page:i.value,page_size:w.value};c.value&&c.value.trim()&&(s.nickname=c.value.trim());const a=await Y(s);a.code===0?(y.value=a.data.users,C.value=a.data.total):_.error(a.message||"获取用户列表失败")}catch(s){console.error("获取用户列表失败:",s),_.error("获取用户列表失败")}finally{v.value=!1}},z=()=>{i.value=1,u()},V=()=>{c.value="",i.value=1,u()},L=s=>{w.value=s,i.value=1,u()},B=s=>{i.value=s,u()},U=async s=>{try{await Q.confirm(`确定要为用户 ${s.phone} 开通会员吗？`,"确认开通",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const a=await Z(s.id.toString());a.code===0?(_.success("开通会员成功"),await u()):_.error(a.message||"开通会员失败")}catch(a){a!=="cancel"&&(console.error("开通会员失败:",a),_.error("开通会员失败"))}},D=s=>{_.info(`编辑用户: ${s.phone}`)},x=s=>s?new Date(s).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}):"-";return F(()=>{u()}),(s,a)=>{const h=o("el-icon"),f=o("el-button"),M=o("el-input"),r=o("el-table-column"),S=o("el-avatar"),$=o("el-tag"),E=o("el-table"),K=o("el-pagination"),N=G("loading");return k(),H("div",ee,[a[4]||(a[4]=l("div",{class:"page-header"},[l("div",{class:"breadcrumb"},[l("span",{class:"breadcrumb-item"},"用户管理")])],-1)),l("div",ae,[l("div",te,[e(f,{type:"primary",size:"small"},{default:n(()=>[e(h,null,{default:n(()=>[e(b(R))]),_:1}),a[1]||(a[1]=m(" 新增用户 "))]),_:1,__:[1]})]),l("div",ne,[e(M,{modelValue:c.value,"onUpdate:modelValue":a[0]||(a[0]=t=>c.value=t),placeholder:"请输入昵称搜索",class:"search-input",size:"small",clearable:"",onKeyup:J(z,["enter"]),onClear:V},{append:n(()=>[e(f,{onClick:z},{default:n(()=>[e(h,null,{default:n(()=>[e(b(W))]),_:1}),a[2]||(a[2]=m(" 搜索 "))]),_:1,__:[2]})]),_:1},8,["modelValue"])])]),l("div",se,[P((k(),A(E,{data:y.value,class:"data-table",stripe:"",border:"","header-cell-style":{background:"#fafafa",color:"#333"}},{default:n(()=>[e(r,{type:"selection",width:"55",align:"center"}),e(r,{prop:"id",label:"用户ID","min-width":"80",align:"center"}),e(r,{prop:"phone",label:"手机号","min-width":"130",align:"center"}),e(r,{prop:"nickname",label:"昵称","min-width":"120"},{default:n(t=>[l("span",null,p(t.row.nickname||"-"),1)]),_:1}),e(r,{label:"头像","min-width":"80",align:"center"},{default:n(t=>[e(S,{size:32,src:t.row.avatar,class:"user-avatar"},{default:n(()=>[e(h,null,{default:n(()=>[e(b(X))]),_:1})]),_:2},1032,["src"])]),_:1}),e(r,{label:"会员状态","min-width":"100",align:"center"},{default:n(t=>[e($,{type:t.row.is_member?"success":"info",size:"small"},{default:n(()=>[m(p(t.row.is_member?"会员":"普通用户"),1)]),_:2},1032,["type"])]),_:1}),e(r,{label:"剩余次数","min-width":"100",align:"center"},{default:n(t=>[l("span",{class:O({"low-usage":t.row.remaining_usage<=5&&t.row.is_member})},p(t.row.is_member?t.row.remaining_usage:"-"),3)]),_:1}),e(r,{prop:"created_at",label:"注册时间","min-width":"160",align:"center"},{default:n(t=>[l("span",null,p(x(t.row.created_at)),1)]),_:1}),e(r,{prop:"updated_at",label:"最后更新","min-width":"160",align:"center"},{default:n(t=>[l("span",null,p(x(t.row.updated_at)),1)]),_:1}),e(r,{label:"操作",width:"120",align:"center",fixed:"right"},{default:n(t=>[e(f,{type:"text",size:"small",onClick:I=>U(t.row),disabled:t.row.is_member},{default:n(()=>[m(p(t.row.is_member?"已开通":"开通会员"),1)]),_:2},1032,["onClick","disabled"]),e(f,{type:"text",size:"small",onClick:I=>D(t.row)},{default:n(()=>a[3]||(a[3]=[m(" 编辑会员 ")])),_:2,__:[3]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[N,v.value]])]),e(K,{class:"pagination","current-page":i.value,"page-size":w.value,"page-sizes":[10,20,50,100],total:C.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:L,onCurrentChange:B},null,8,["current-page","page-size","total"])])}}},oe=q(le,[["__scopeId","data-v-4ff15c60"]]);export{oe as default};
